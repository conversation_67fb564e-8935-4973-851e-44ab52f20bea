cmake_minimum_required(VERSION 3.10)
project(shunzao_ai_demo)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_SYSTEM_PROCESSOR arm)

set(TARGET_BOARD "MR536" CACHE STRING "Target board type")

message(STATUS "Target board: ${TARGET_BOARD}")

# -------------------------- 平台分支配置 --------------------------
if (CMAKE_SYSTEM_NAME STREQUAL "Android")
    message (STATUS "USE NDK BUILD")
else()
message (STATUS "USE arm-aarch64 BUILD")
    # 远程服务器编译
    # set(CMAKE_C_COMPILER "${CMAKE_SOURCE_DIR}/../mr536_toolchain/bin/aarch64-openwrt-linux-gnu-gcc")
    # set(CMAKE_CXX_COMPILER "${CMAKE_SOURCE_DIR}/../mr536_toolchain/bin/aarch64-openwrt-linux-gnu-g++")
    # # 本地编译
    # # set(CMAKE_C_COMPILER "${CMAKE_SOURCE_DIR}/../0-toolchains/mr536_toolchain/bin/aarch64-openwrt-linux-gcc")
    # # set(CMAKE_CXX_COMPILER "${CMAKE_SOURCE_DIR}/../0-toolchains/mr536_toolchain/bin/aarch64-openwrt-linux-g++")

    # # 在编译器设置之后添加 sysroot 配置 （不需要设置）
    # # set(CMAKE_SYSROOT "${CMAKE_SOURCE_DIR}/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc")
    # set(CMAKE_SYSROOT "${CMAKE_SOURCE_DIR}/../mr536_toolchain/aarch64-openwrt-linux-gnu")  # 标准库头文件和库的路径
    set(CMAKE_C_COMPILER "${CMAKE_SOURCE_DIR}/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc")
    set(CMAKE_CXX_COMPILER "${CMAKE_SOURCE_DIR}/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++")
    # 在编译器设置之后添加 sysroot 配置
    set(CMAKE_SYSROOT "${CMAKE_SOURCE_DIR}/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc")

endif()


# 系统架构判断
# if(CMAKE_C_COMPILER MATCHES "android-ndk")
#     set(SYS_ARCH android_aarch64)
# elseif(CMAKE_C_COMPILER MATCHES "aarch64")
#     set(SYS_ARCH linux_aarch64)
#     add_definitions(-D_GLIBCXX_USE_CXX11_ABI=0)
# else()
#     set(SYS_ARCH linux_armhf)
#     add_definitions(-DLIBVIP_VERSION_85X)
# endif()

set(SYS_ARCH linux_aarch64)
add_definitions(-D_GLIBCXX_USE_CXX11_ABI=1)

# -------------------------- 编译选项 --------------------------
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2 -fopenmp -fdiagnostics-color=always")


# OpenMP配置
# if(SYS_ARCH MATCHES "linux_armhf|android_aarch64")
#     find_package(OpenMP REQUIRED)
#     if(OPENMP_FOUND)
#         set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
#         set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
#         set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${OpenMP_EXE_LINKER_FLAGS}")
#     endif()
# endif()
if(SYS_ARCH STREQUAL "linux_armhf")
    FIND_PACKAGE(OpenMP REQUIRED)
elseif(SYS_ARCH STREQUAL "android_aarch64")
    FIND_PACKAGE(OpenMP REQUIRED)
endif()

if(OPENMP_FOUND)
    message("OPENMP FOUND")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${OpenMP_EXE_LINKER_FLAGS}")
endif()

# -------------------------- 依赖配置 --------------------------
# OpenCV配置
if(SYS_ARCH STREQUAL "linux_armhf")
    set(OpenCV_DIR "${CMAKE_SOURCE_DIR}/../3rdparty/opencv/opencv-3.4.16-gnueabihf-linux/share/OpenCV")
    message("SYS_ARCH: ${SYS_ARCH}")
elseif(SYS_ARCH STREQUAL "linux_aarch64")
    message("SYS_ARCH: ${SYS_ARCH}")
    # 本地编译的配置位置
    # set(OpenCV_DIR "${CMAKE_SOURCE_DIR}/../0-toolchains/mr536_toolchain/aarch64-openwrt-linux-gnu/lib/cmake/opencv4")
    # set(OpenCV_DIR "${CMAKE_SOURCE_DIR}/../3rdparty/opencv4.9lib/lib/cmake/opencv4")
    #服务器编译的配置
    set(OpenCV_DIR "${CMAKE_SOURCE_DIR}/../3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/cmake/opencv4")
    # set(OpenCV_DIR "${CMAKE_SOURCE_DIR}/../opencv4.9lib/lib/cmake/opencv4")
    # set(OpenCV_DIR "${CMAKE_SOURCE_DIR}/../mr536_toolchain/aarch64-openwrt-linux-gnu/lib/cmake/opencv4")
elseif(SYS_ARCH STREQUAL "android_aarch64")
    message("SYS_ARCH: ${SYS_ARCH}")
    set(OpenCV_DIR "${CMAKE_SOURCE_DIR}/../3rdparty/opencv/opencv-4.9.0-android/sdk/native/jni/abi-${ANDROID_ABI}")
else()
    message(FATAL_ERROR "Unsupported SYS_ARCH: ${SYS_ARCH}")
endif()

# 查找所需的库
find_package(OpenCV REQUIRED COMPONENTS core imgproc imgcodecs highgui)
# find_package(OpenCV REQUIRED COMPONENTS)
message("opencv path: ${OpenCV_DIR}")
message("opencv include path: ${OpenCV_INCLUDE_DIRS}")
message("opencv libs: ${OpenCV_LIBS}")

if(OpenCV_FOUND)
    message(STATUS "OpenCV library status:")
    message(STATUS "    version: ${OpenCV_VERSION}")
    message(STATUS "    libraries: ${OpenCV_LIBS}")
    message(STATUS "    include path: ${OpenCV_INCLUDE_DIRS}")
endif()

add_compile_options(-D_GLIBCXX_USE_CXX11_ABI=0)
# include_directories(/opt/tools/mr536_toolchain/aarch64-openwrt-linux-gnu/include/opencv4)
# link_directories("/opt/tools/mr536_toolchain/aarch64-openwrt-linux-gnu/lib")


# VIP库配置
set(VIP_LIB_DIR "${CMAKE_SOURCE_DIR}/../common/lib_${SYS_ARCH}/${TARGET_BOARD}")
file(GLOB VIP_LIBS "${VIP_LIB_DIR}/*.so*")
message("VIP_LIBS: ${VIP_LIBS}")

# set(LOG_LIB "/home/<USER>/code_project/MR536/tina-mr536-v1.2/prebuilt/hostbuilt/linux-x86/lib64/liblog.so")


# -------------------------- 源文件管理 --------------------------
file(GLOB_RECURSE SOURCE_FILES
    "${PROJECT_SOURCE_DIR}/src/*.cc"
    # "${PROJECT_SOURCE_DIR}/*.cc"
    "${PROJECT_SOURCE_DIR}/../common/*.cpp"
)
aux_source_directory(${PROJECT_SOURCE_DIR}/src/utils DIR_utils_SRCS)
aux_source_directory(${PROJECT_SOURCE_DIR}/src/algorithm DIR_algorithm_SRCS)
aux_source_directory(${PROJECT_SOURCE_DIR}/src/basic_model DIR_basic_SRCS)
aux_source_directory(${PROJECT_SOURCE_DIR}/src/task DIR_task_SRCS)
aux_source_directory(${PROJECT_SOURCE_DIR}/src DIR_shunzaolib_SRCS)
# aux_source_directory(${PROJECT_SOURCE_DIR}/../libawnn_viplite DIR_viplite_SRCS)
# -------------------------- 头文件包含 --------------------------
# include_directories(${PROJECT_SOURCE_DIR}/../../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/libc/usr/include)

include_directories(
    # ${CMAKE_SYSROOT}/usr/include
    # ${CMAKE_SYSROOT}/include/opencv4
    # ${CMAKE_SYSROOT}/include
    ${OpenCV_INCLUDE_DIRS} # OpenCV头文件路径
    ${CMAKE_SOURCE_DIR}/../common/include
    ${CMAKE_SOURCE_DIR}/../common
    ${PROJECT_SOURCE_DIR}/include
    ${PROJECT_SOURCE_DIR}/include/algorithm
    ${PROJECT_SOURCE_DIR}/include/utils
    ${PROJECT_SOURCE_DIR}/include/basic_model
    ${PROJECT_SOURCE_DIR}/include/task
    # ${PROJECT_SOURCE_DIR}/../libawnn_viplite  # 是否可以删除？
)

# link_directories("${CMAKE_SYSROOT}/lib")


if(SYS_ARCH STREQUAL "linux_armhf")
    include_directories(${PROJECT_SOURCE_DIR}/../common/include_85x)
else()
    include_directories(${PROJECT_SOURCE_DIR}/../common/include)
endif()



# -------------------------- 构建目标 --------------------------

# 1) 先生成共享库 shunzao_ai_lib，并把所有依赖都链接进去
add_library(shunzao_ai_lib SHARED
    ${SOURCE_FILES}
    ${DIR_utils_SRCS}
    ${DIR_algorithm_SRCS}
    ${DIR_basic_SRCS}
    ${DIR_task_SRCS}
    ${DIR_shunzaolib_SRCS}
    # ${DIR_viplite_SRCS}
    
)
target_link_libraries(shunzao_ai_lib
    ${VIP_LIBS}
    ${OpenCV_LIBS}
    pthread
    m
    dl
)

# 2) 再生成可执行文件 shunzao_ai_demo，**注意这里的名字要和 project() 一致**
add_executable(shunzao_ai_demo
    ${PROJECT_SOURCE_DIR}/main.cc
    # ${SOURCE_FILES}
    # ${DIR_viplite_SRCS}
)
# 3) 把 shunzao_ai_lib 链到可执行文件上
target_link_libraries(shunzao_ai_demo PRIVATE
    ${OpenCV_LIBS}    
    shunzao_ai_lib
    
)

# -------------------------- 输出 & 安装 --------------------------
# install 可执行文件到 <prefix>/bin
install(TARGETS shunzao_ai_demo
    RUNTIME DESTINATION bin
)

# install 共享库到 <prefix>/lib
install(TARGETS shunzao_ai_lib
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib        # 如果也有静态库
)

